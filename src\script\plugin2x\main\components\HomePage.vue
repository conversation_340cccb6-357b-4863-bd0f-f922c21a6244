<template>
    <div class="home-page">
        <!-- 侧边栏组件 -->
        <Sidebar
            :menu-groups="menuGroups"
            :active-menu-item="activeMenuItem"
            :is-ai-responding="isAiResponding"
            @new-chat="handleNewChat"
            @search-select="handleSearchSelect"
            @menu-item-click="handleMenuItemClick"
            @delete-session="handleDeleteSession"
            @stop-generation="handleStopGeneration"
        />

        <!-- 主内容区域组件 -->
        <MainContent
            v-if="currentView === 'home'"
            :welcome-config="welcomeConfig"
            :input-config="inputConfig"
            :feature-cards="featureCards"
            :model-options="modelOptions"
            :defaultModel="defaultModel"
            :selectedModel="selectedModel"
            :is-ai-responding="isAiResponding"
            @card-click="handleCardClick"
            @send-message="handleSendMessage"
            @model-change="handleModelChange"
        />

        <!-- 聊天页面组件（预留） -->
        <ChatPage
            v-if="currentView === 'chat'"
            :title="currentSessionTitle"
            :session-id="currentSessionId"
            :messages="chatMessages"
            :model-options="modelOptions"
            :defaultModel="defaultModel"
            :selectedModel="selectedModel"
            :is-ai-responding="isAiResponding"
            :is-stream-response="isStreamResponse"
            @send-message="handleSendMessage"
            @model-change="handleModelChange"
            @stop-generation="handleStopGeneration"
            @update-title="handleUpdateSessionTitle"
        />
    </div>
</template>

<script>
import Sidebar from '@/script/components/Sidebar.vue';
import MainContent from '@/script/components/MainContent.vue';
import ChatPage from '@/script/components/ChatPage.vue';
import AssistantApi from '@/script/api/module/assistant.js';
import chatStorageService from '@/script/utils/chatStorage.js';
import axios from 'axios';

export default {
    name: 'HomePage',
    components: {
        Sidebar,
        MainContent,
        ChatPage
    },
    data() {
        return {
            currentView: 'home', // 'home' 或 'chat'
            activeMenuItem: null,
            chatMessages: [], // 聊天消息列表
            selectedModel: 'DifyStream', // 当前选择的模型
            isAiResponding: false, // AI是否正在回复
            isStreamResponse: false, // 是否为流式响应
            currentCancelTokenSource: null, // 当前请求的取消令牌源

            // 会话管理相关状态
            currentSessionId: null, // 当前会话ID
            chatSessions: [], // 聊天会话列表
            isStorageAvailable: false, // 存储服务是否可用
            isTemporarySession: false, // 当前是否为临时会话（未保存）

            welcomeConfig: {
                title: '你好，欢迎使用全新智能助手',
                description:
                    '智能对话新体验，助您轻松了解不同需求的适用数据，用AI开启高效便捷的数据订阅之旅'
            },

            inputConfig: {
                placeholder:
                    '请输入您的问题，Shift+Enter可换行，@后带能力名称可指定能力，输入后按Enter发送'
            },
            modelOptions: [
                // { label: 'DeepSeek', value: 'DeepSeek', desc: '无接入知识库，可简单测试问答' },
                // { label: 'Dify', value: 'Dify', desc: '接入数据资产知识库，最终使用该接口问答' },
                // { label: 'OpenAI/vLLM', value: 'OpenAI/vLLM', desc: '本地部署的vLLM模型服务' },
                {
                    label: 'Dify',
                    value: 'DifyStream',
                    desc: '接入数据资产知识库'
                }
            ],

            featureCards: [
                {
                    id: 'location-capability',
                    bg: require('@/img/main/card-bg-1.png'),
                    icon: require('@/img/main/card-logo-1.png'),
                    title: '位置能力使用推荐',
                    description:
                        '根据用户需求及业务口径，结合开放目录、区域洞察AP1、实时事件、平台指标，将匹配的能力以不同的组件形式向用户展示。',
                    action: 'recommend-location'
                },
                {
                    id: 'asset-subscription',
                    bg: require('@/img/main/card-bg-2.png'),
                    icon: require('@/img/main/card-logo-2.png'),
                    title: '位置资产订购向导',
                    description:
                        '分析用户需求，引导用户订购推荐的数据资产(API/数据开放目录表等)，同时输出可视化操作步骤指引用户订购。',
                    action: 'asset-qa'
                },
                {
                    id: 'business-requirements',
                    bg: require('@/img/main/card-bg-3.png'),
                    icon: require('@/img/main/card-logo-3.png'),
                    title: '位置业务需求规格',
                    description:
                        '提取用户需求中的时空信息，包括业务背景、业务口径、数据账期地理范围，以格式化方式呈现，支持生成并下载需求规格说明书。',
                    action: 'business-data'
                }
            ]
        };
    },

    computed: {
        // 默认模型
        defaultModel() {
            return this.modelOptions[0].value;
        },
        // 动态生成菜单组
        menuGroups() {
            if (!this.isStorageAvailable || this.chatSessions.length === 0) {
                return [];
            }

            return this.groupSessionsByTime(this.chatSessions);
        },

        // 当前会话标题
        currentSessionTitle() {
            if (!this.currentSessionId || !this.isStorageAvailable) {
                return '新对话';
            }

            // 如果是临时会话，直接返回"新对话"
            if (this.isTemporarySession) {
                return '新对话';
            }

            const session = this.chatSessions.find((s) => s.id === this.currentSessionId);
            if (session) {
                return session.title;
            }
            return '新对话';
        }
    },

    async mounted() {
        // 初始化聊天存储服务
        this.isStorageAvailable = chatStorageService.init();

        if (this.isStorageAvailable) {
            // 加载聊天会话列表
            await this.loadChatSessions();

            // 恢复当前会话
            await this.restoreCurrentSession();
        }
    },

    methods: {
        // 处理新对话
        async handleNewChat() {
            // 如果AI正在回复，禁用新建对话功能
            if (this.isAiResponding) {
                return;
            }

            // 清理当前的临时会话（如果存在且为空）
            await this.cleanupEmptyTemporarySession();

            // 创建临时会话（不保存到存储）
            this.currentSessionId = this.generateTempSessionId();
            this.chatMessages = [];
            this.activeMenuItem = this.currentSessionId;
            this.isTemporarySession = true;

            this.currentView = 'home';
        },

        // 加载聊天会话列表
        async loadChatSessions() {
            if (!this.isStorageAvailable) return;

            try {
                this.chatSessions = chatStorageService.getAllSessions();
            } catch (error) {
                console.error('加载聊天会话失败:', error);
                this.chatSessions = [];
            }
        },

        // 恢复当前会话
        async restoreCurrentSession() {
            if (!this.isStorageAvailable) return;

            try {
                const currentSessionId = chatStorageService.getCurrentSessionId();
                if (!currentSessionId) return;

                // 检查是否为临时会话ID，如果是则不恢复
                if (currentSessionId.startsWith('temp_')) {
                    chatStorageService.setCurrentSessionId(null);
                    return;
                }

                const session = chatStorageService.getSession(currentSessionId);
                if (!session) return;

                this.currentSessionId = currentSessionId;
                if (session.messages) {
                    this.chatMessages = session.messages;
                } else {
                    this.chatMessages = [];
                }
                if (session.model && this.modelOptions.find((m) => m.value === session.model)) {
                    this.selectedModel = session.model;
                } else {
                    this.selectedModel = this.defaultModel;
                }
                this.activeMenuItem = currentSessionId;
                this.isTemporarySession = false;

                // 如果有消息，切换到聊天页面
                if (this.chatMessages.length > 0) {
                    this.currentView = 'chat';
                }
            } catch (error) {
                console.error('恢复当前会话失败:', error);
            }
        },

        // 按时间分组会话
        groupSessionsByTime(sessions) {
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

            const groups = {
                today: { key: 'today', title: '今天', items: [] },
                yesterday: { key: 'yesterday', title: '昨天', items: [] },
                week: { key: 'week', title: '一周内', items: [] },
                month: { key: 'month', title: '30天内', items: [] }
            };

            sessions.forEach((session) => {
                const sessionDate = new Date(session.updatedAt);
                const item = {
                    id: session.id,
                    text: session.title,
                    type: 'conversation',
                    timestamp: session.updatedAt,
                    messageCount: session.messageCount || 0
                };

                if (sessionDate >= today) {
                    groups.today.items.push(item);
                } else if (sessionDate >= yesterday) {
                    groups.yesterday.items.push(item);
                } else if (sessionDate >= weekAgo) {
                    groups.week.items.push(item);
                } else if (sessionDate >= monthAgo) {
                    groups.month.items.push(item);
                }
            });

            // 只返回有内容的分组
            return Object.values(groups).filter((group) => group.items.length > 0);
        },

        // 处理搜索选择
        async handleSearchSelect(item) {
            // 如果AI正在回复，禁用切换会话功能
            if (this.isAiResponding) {
                return;
            }

            await this.switchToSession(item.id);
        },

        // 处理菜单项点击
        async handleMenuItemClick(item) {
            // 如果AI正在回复，禁用切换会话功能
            if (this.isAiResponding) {
                return;
            }

            if (item.type === 'conversation') {
                await this.switchToSession(item.id);
            }
        },

        // 切换到指定会话
        async switchToSession(sessionId) {
            if (!this.isStorageAvailable || !sessionId) return;

            try {
                // 清理当前的临时会话（如果存在且为空）
                await this.cleanupEmptyTemporarySession();

                const session = chatStorageService.getSession(sessionId);
                if (session) {
                    this.currentSessionId = sessionId;
                    if (session.messages) {
                        this.chatMessages = session.messages;
                    } else {
                        this.chatMessages = [];
                    }
                    if (session.model && this.modelOptions.find((m) => m.value === session.model)) {
                        this.selectedModel = session.model;
                    } else {
                        this.selectedModel = this.defaultModel;
                    }
                    this.activeMenuItem = sessionId;
                    this.currentView = 'chat';
                    this.isTemporarySession = false;

                    // 更新当前会话ID
                    chatStorageService.setCurrentSessionId(sessionId);
                }
            } catch (error) {
                console.error('切换会话失败:', error);
            }
        },

        // 生成临时会话ID
        generateTempSessionId() {
            return 'temp_' + Date.now().toString(36) + Math.random().toString(36).substring(2);
        },

        // 清理空的临时会话
        async cleanupEmptyTemporarySession() {
            if (this.isTemporarySession && this.chatMessages.length === 0) {
                // 如果当前是空的临时会话，清理状态
                this.currentSessionId = null;
                this.activeMenuItem = null;
                this.isTemporarySession = false;

                // 清除localStorage中的当前会话ID
                if (this.isStorageAvailable) {
                    chatStorageService.setCurrentSessionId(null);
                }
            }
        },

        // 处理删除会话
        async handleDeleteSession(sessionId) {
            if (!this.isStorageAvailable || !sessionId) return;

            try {
                // 删除会话
                const success = chatStorageService.deleteSession(sessionId);
                if (success) {
                    // 重新加载会话列表
                    await this.loadChatSessions();

                    // 如果删除的是当前会话，切换到首页
                    if (this.currentSessionId === sessionId) {
                        this.currentSessionId = null;
                        this.chatMessages = [];
                        this.activeMenuItem = null;
                        this.currentView = 'home';
                    }
                }
            } catch (error) {
                console.error('删除会话失败:', error);
                this.$message.error('删除对话失败，请重试');
            }
        },

        // 处理功能卡片点击
        async handleCardClick() {
            // 创建新会话并切换到聊天页面
            await this.handleNewChat();
        },

        // 处理发送消息
        // eslint-disable-next-line complexity
        async handleSendMessage(message) {
            // 如果正在回复中，不允许发送新消息
            if (this.isAiResponding) {
                return;
            }

            // 确保有当前会话
            await this.ensureCurrentSession(message.model);

            // 添加用户消息到聊天记录
            const userMessage = {
                ...message,
                type: 'user'
            };
            this.chatMessages.push(userMessage);

            // 保存用户消息到存储
            if (this.isStorageAvailable && this.currentSessionId) {
                const saveSuccess = chatStorageService.addMessage(
                    this.currentSessionId,
                    userMessage
                );
                if (saveSuccess) {
                    // 重新加载会话列表以更新侧边栏
                    await this.loadChatSessions();
                } else {
                    console.warn('保存用户消息失败');
                }
            }

            // 切换到聊天页面
            this.currentView = 'chat';

            // 设置AI回复状态
            this.isAiResponding = true;

            // 创建CancelToken用于取消请求
            this.currentCancelTokenSource = axios.CancelToken.source();

            // 调用 AI API 获取回复
            try {
                // 构建聊天历史
                const messages = this.chatMessages
                    .filter((msg) => msg.type === 'user' || msg.type === 'assistant')
                    .map((msg) => {
                        let role;
                        if (msg.type === 'user') {
                            role = 'user';
                        } else {
                            role = 'assistant';
                        }
                        return {
                            role: role,
                            content: msg.text
                        };
                    });

                // 根据选择的模型调用对应的服务
                const apiParams = {
                    model: this.getModelName(message.model),
                    messages: messages,
                    max_tokens: 2048,
                    temperature: 0.7
                };

                let response;
                const service = this.getServiceByModel(message.model);
                const cancelToken = this.currentCancelTokenSource.token;
                const isStream = this.isStreamModel(message.model);

                if (isStream) {
                    // 设置流式响应状态
                    this.isStreamResponse = true;
                    // 流式响应处理
                    await this.handleStreamResponse(service, apiParams, cancelToken, message);
                } else {
                    // 设置非流式响应状态
                    this.isStreamResponse = false;
                    // 传统响应处理
                    if (service === 'deepseek') {
                        response = await AssistantApi.deepseekChat(apiParams, cancelToken);
                    } else if (service === 'dify') {
                        apiParams.parameters = {
                            industry: '位置大数据',
                            analysis_type: 'general',
                            user_id: 'user_001'
                        };
                        response = await AssistantApi.difyChat(apiParams, cancelToken);
                    } else if (service === 'openai') {
                        response = await AssistantApi.openaiChat(apiParams, cancelToken);
                    } else {
                        // 默认使用 DeepSeek
                        response = await AssistantApi.defaultChat(apiParams, cancelToken);
                    }
                }

                // 处理传统 API 响应
                if (!isStream) {
                    if (response && response.choices && response.choices.length > 0) {
                        const aiResponse = {
                            text: response.choices[0].message.content,
                            type: 'assistant',
                            timestamp: new Date().toISOString(),
                            model: message.model
                        };
                        this.chatMessages.push(aiResponse);

                        // 保存AI回复到存储
                        if (this.isStorageAvailable && this.currentSessionId) {
                            chatStorageService.addMessage(this.currentSessionId, aiResponse);
                            // 重新加载会话列表以更新侧边栏
                            await this.loadChatSessions();
                        }
                    } else {
                        throw new Error('API 响应格式错误');
                    }
                }
            } catch (error) {
                console.error('API 调用失败:', error);

                // 检查是否是用户主动取消的请求
                if (error && error.isCancelled) {
                    // 用户取消了请求，不显示错误消息
                } else {
                    // 根据新的错误格式处理错误消息
                    let errorMessage = '未知错误';
                    if (error && typeof error === 'object') {
                        if (error.errorMessage) {
                            errorMessage = error.errorMessage;
                        } else if (error.message) {
                            errorMessage = error.message;
                        } else if (typeof error === 'string') {
                            errorMessage = error;
                        }
                    }

                    // 添加错误消息
                    const errorResponse = {
                        text: `抱歉，服务暂时不可用。错误信息：${errorMessage}`,
                        type: 'assistant',
                        timestamp: new Date().toISOString(),
                        isError: true
                    };
                    this.chatMessages.push(errorResponse);
                }
            } finally {
                // 重置AI回复状态
                this.isAiResponding = false;
                this.currentCancelTokenSource = null;
            }
        },

        // 处理停止生成
        handleStopGeneration() {
            if (this.currentCancelTokenSource && (this.isAiResponding || this.isStreamResponse)) {
                // 取消当前请求
                this.currentCancelTokenSource.cancel('用户停止了AI回复生成');

                // 添加停止操作的消息记录
                const stopMessage = {
                    text: '[用户停止了AI回复生成]',
                    type: 'system',
                    timestamp: new Date().toISOString(),
                    isStopAction: true
                };
                this.chatMessages.push(stopMessage);

                // 保存停止消息到存储
                if (this.isStorageAvailable && this.currentSessionId) {
                    chatStorageService.addMessage(this.currentSessionId, stopMessage);
                    // 重新加载会话列表以更新侧边栏
                    this.loadChatSessions();
                }

                // 重置状态
                this.isAiResponding = false;
                this.isStreamResponse = false;
                this.currentCancelTokenSource = null;
            }
        },

        // 根据模型获取对应的服务
        getServiceByModel(model) {
            const serviceMap = {
                DeepSeek: 'deepseek',
                Dify: 'dify',
                'OpenAI/vLLM': 'openai',
                DifyStream: 'dify'
            };
            return serviceMap[model] || 'deepseek';
        },

        // 检查是否为流式模型（仅支持 Dify 流式）
        isStreamModel(model) {
            return model === 'DifyStream';
        },

        // 处理流式响应
        async handleStreamResponse(service, apiParams, cancelToken, message) {
            // 创建流式响应的消息对象
            const streamMessage = {
                text: '',
                type: 'assistant',
                timestamp: new Date().toISOString(),
                model: message.model,
                isStreaming: true
            };

            // 添加到消息列表
            this.chatMessages.push(streamMessage);

            // 为 Dify 服务添加特殊参数
            if (service === 'dify') {
                apiParams.parameters = {
                    industry: '位置大数据',
                    analysis_type: 'general',
                    user_id: 'user_001'
                };
            }

            // 流式数据处理回调
            const onData = (data) => {
                let content = '';

                // 处理不同格式的流式数据
                if (data.text) {
                    // Dify 流式格式（从 answer 字段提取的文本）
                    content = data.text;
                } else if (data.choices && data.choices.length > 0) {
                    // OpenAI 格式（兼容性保留）
                    const delta = data.choices[0].delta;
                    if (delta && delta.content) {
                        content = delta.content;
                    }
                } else if (data.content) {
                    // 直接内容格式
                    content = data.content;
                } else if (typeof data === 'string') {
                    // 纯字符串格式
                    content = data;
                }

                // 更新消息内容
                if (content) {
                    // 第一个数据包到达时立即隐藏加载状态，但保持流式状态
                    if (this.isAiResponding) {
                        this.isAiResponding = false;
                        // 确保流式状态正确设置
                        this.isStreamResponse = true;
                    }

                    // 确保数据完整性，使用 Vue.set 触发响应式更新
                    const currentText = streamMessage.text || '';
                    const newText = currentText + content;

                    // 使用 Vue.set 确保响应式更新
                    this.$set(streamMessage, 'text', newText);

                    // 确保 isStreaming 状态正确
                    this.$set(streamMessage, 'isStreaming', true);

                    // 使用防抖机制避免过度渲染
                    this.$nextTick(() => {
                        this.$forceUpdate();
                    });
                }
            };

            // 流式完成回调
            const onComplete = async () => {
                // 标记流式响应完成
                this.$set(streamMessage, 'isStreaming', false);

                // 确保最终文本完整性和状态重置
                this.$nextTick(() => {
                    // 强制更新以确保 Markdown 渲染完成
                    this.$forceUpdate();

                    // 保存完整消息到存储
                    if (this.isStorageAvailable && this.currentSessionId) {
                        chatStorageService.addMessage(this.currentSessionId, streamMessage);
                        // 重新加载会话列表以更新侧边栏
                        this.loadChatSessions();
                    }

                    // 延迟重置流式状态，确保组件有足够时间完成最终渲染
                    setTimeout(() => {
                        this.isStreamResponse = false;
                    }, 100);
                });

                // 重置所有相关状态
                this.isAiResponding = false;
                this.currentCancelTokenSource = null;
            };

            // 流式错误回调
            const onError = (error) => {
                if (error.isCancelled) {
                    // 重置所有相关状态
                    this.isAiResponding = false;
                    this.isStreamResponse = false;
                    this.currentCancelTokenSource = null;
                    return;
                }
                console.error('流式响应错误:', error);

                // 标记消息为错误状态
                this.$set(streamMessage, 'isError', true);
                this.$set(streamMessage, 'isStreaming', false);

                // 如果没有接收到任何内容，显示错误信息
                if (!streamMessage.text) {
                    this.$set(streamMessage, 'text', '抱歉，生成回复时出现错误，请重试。');
                }

                // 强制更新UI
                this.$nextTick(() => {
                    this.$forceUpdate();
                });

                // 重置所有相关状态
                this.isAiResponding = false;
                this.isStreamResponse = false;
                this.currentCancelTokenSource = null;
            };

            // 调用对应的流式API（仅支持 Dify）
            try {
                if (service === 'dify') {
                    await AssistantApi.difyStreamChatFlow(
                        apiParams,
                        cancelToken,
                        onData,
                        onComplete,
                        onError
                    );
                } else {
                    // 其他服务暂不支持流式响应
                    throw new Error(`服务 ${service} 暂不支持流式响应`);
                }
            } catch (error) {
                // 如果不是取消错误，调用错误处理
                if (!error.isCancelled) {
                    onError(error);
                }
            }
        },

        // 获取实际的模型名称
        getModelName(model) {
            const modelMap = {
                DeepSeek: 'deepseek-chat',
                Dify: 'dify-chatflow',
                'OpenAI/vLLM': 'qwen3-30b-a3b',
                DifyStream: 'dify-chatflow'
            };
            return modelMap[model] || 'deepseek-chat';
        },

        // 确保有当前会话
        async ensureCurrentSession(model) {
            if (!this.isStorageAvailable) return;

            if (!this.currentSessionId || this.isTemporarySession) {
                // 如果是临时会话，转为正式会话
                if (this.isTemporarySession) {
                    const newSession = chatStorageService.createSession({
                        model: model || this.selectedModel
                    });

                    this.currentSessionId = newSession.id;
                    this.activeMenuItem = newSession.id;
                    this.isTemporarySession = false;
                } else {
                    // 创建新会话
                    const newSession = chatStorageService.createSession({
                        model: model || this.selectedModel
                    });

                    this.currentSessionId = newSession.id;
                    this.activeMenuItem = newSession.id;
                }

                // 重新加载会话列表
                await this.loadChatSessions();
            }
        },

        // 处理模型变更
        async handleModelChange(newModel) {
            this.selectedModel = newModel;

            // 如果有当前会话，更新会话的模型
            if (this.isStorageAvailable && this.currentSessionId) {
                chatStorageService.saveSession(this.currentSessionId, { model: newModel });
            }
        },

        // 处理会话标题更新
        async handleUpdateSessionTitle(sessionId, newTitle) {
            if (!this.isStorageAvailable || !sessionId || !newTitle) return;

            // 不允许更新临时会话的标题
            if (sessionId.startsWith('temp_')) {
                console.warn('不能更新临时会话的标题');
                return;
            }

            try {
                const success = chatStorageService.updateSessionTitle(sessionId, newTitle);
                if (success) {
                    // 重新加载会话列表以更新侧边栏
                    await this.loadChatSessions();
                }
            } catch (error) {
                console.error('更新会话标题失败:', error);
                this.$message.error('更新标题失败，请重试');
            }
        }
    }
};
</script>

<style scoped lang="less">
.home-page {
    display: flex;
    background-image: url('~@/img/main/main-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center center;
    height: 100vh;
}
</style>
